buildscript {
    repositories {
        maven { url "https://plugins.gradle.org/m2/" }
        maven { url "https://repo.grails.org/grails/core" }
    }
    dependencies {
        classpath "org.grails:grails-gradle-plugin:$grailsGradlePluginVersion"
        classpath "gradle.plugin.com.github.erdi.webdriver-binaries:webdriver-binaries-gradle-plugin:2.6"
        classpath "org.grails.plugins:hibernate5:7.3.0"
        classpath "com.bertramlabs.plugins:asset-pipeline-gradle:3.4.7"
    }
}

version "0.1"
group "ws_v2"

apply plugin:"eclipse"
apply plugin:"idea"
apply plugin:"war"
apply plugin:"org.grails.grails-web"
apply plugin:"com.github.erdi.webdriver-binaries"
apply plugin:"org.grails.grails-gsp"
apply plugin:"com.bertramlabs.asset-pipeline"

repositories {
    mavenCentral()
    maven { url "https://repo.grails.org/grails/core" }
}

configurations {
    developmentOnly
    runtimeClasspath {
        extendsFrom developmentOnly
    }
}

dependencies {
    developmentOnly("org.springframework.boot:spring-boot-devtools")
    compileOnly "io.micronaut:micronaut-inject-groovy"
    console "org.grails:grails-console"
    implementation "org.springframework.boot:spring-boot-starter-logging"
    implementation "ch.qos.logback:logback-classic:1.2.12"
    implementation "ch.qos.logback:logback-core:1.2.12"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    implementation "org.springframework.boot:spring-boot-autoconfigure"
    implementation "org.grails:grails-core"
    implementation "org.springframework.boot:spring-boot-starter-actuator"
    implementation "org.springframework.boot:spring-boot-starter-tomcat"
    implementation "org.grails:grails-web-boot"
    implementation "org.grails:grails-logging"
    implementation "org.grails:grails-plugin-rest"
    implementation "org.grails:grails-plugin-databinding"
    implementation "org.grails:grails-plugin-i18n"
    implementation "org.grails:grails-plugin-services"
    implementation "org.grails:grails-plugin-url-mappings"
    implementation "org.grails:grails-plugin-interceptors"
    implementation "org.grails.plugins:cache"
    implementation "org.grails.plugins:async"
    implementation "org.grails.plugins:scaffolding"
    implementation "org.grails.plugins:hibernate5"
    implementation "org.hibernate:hibernate-core:5.6.11.Final"
    implementation "org.hibernate:hibernate-jcache:5.6.11.Final"
    implementation "org.ehcache:ehcache:3.10.8"
    implementation "org.grails.plugins:events"
    implementation "org.grails.plugins:gsp"
    implementation "org.grails.plugins:spring-security-core:5.1.1"
    implementation "org.grails.plugins:spring-security-rest:3.0.1"
    implementation 'mysql:mysql-connector-java:8.0.17'
    implementation 'com.zaxxer:HikariCP:5.0.1'

    // Redis Plugin for Grails 5.3.6
    implementation "org.grails.plugins:redis:2.0.5"

    // Excel Processing Libraries
    implementation 'net.sourceforge.jexcelapi:jxl:2.6.12'
    implementation 'org.apache.poi:poi:5.2.3'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'

    // Apache Commons Libraries
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'commons-io:commons-io:2.11.0'

    // Rendering Plugin for PDF/Image processing - will add later when compatible version is found
    // implementation "org.grails.plugins:rendering:2.0.1"
    profile "org.grails.profiles:web"
    runtimeOnly "org.glassfish.web:el-impl:2.2.1-b05"
    runtimeOnly "com.h2database:h2"
    runtimeOnly "org.apache.tomcat:tomcat-jdbc"
    runtimeOnly "javax.xml.bind:jaxb-api:2.3.1"
    runtimeOnly "com.bertramlabs.plugins:asset-pipeline-grails:3.4.7"
    testImplementation "io.micronaut:micronaut-inject-groovy"
    testImplementation "org.grails:grails-gorm-testing-support"
    testImplementation "org.mockito:mockito-core"
    testImplementation "org.grails:grails-web-testing-support"
    testImplementation "org.grails.plugins:geb"
    testImplementation "org.seleniumhq.selenium:selenium-remote-driver:4.0.0"
    testImplementation "org.seleniumhq.selenium:selenium-api:4.0.0"
    testImplementation "org.seleniumhq.selenium:selenium-support:4.0.0"
    testRuntimeOnly "org.seleniumhq.selenium:selenium-chrome-driver:4.0.0"
    testRuntimeOnly "org.seleniumhq.selenium:selenium-firefox-driver:4.0.0"
}

bootRun {
    ignoreExitValue true
    jvmArgs(
        '-Dspring.output.ansi.enabled=always',
        '-noverify',
        '-XX:TieredStopAtLevel=1',
        '-Xmx1024m')
    sourceResources sourceSets.main
    String springProfilesActive = 'spring.profiles.active'
    systemProperty springProfilesActive, System.getProperty(springProfilesActive)
}

springBoot {
    mainClass = 'ws_v2.Application'
}

tasks.withType(GroovyCompile) {
    configure(groovyOptions) {
        forkOptions.jvmArgs = ['-Xmx1024m']
    }
}

tasks.withType(Test) {
    useJUnitPlatform()
}

webdriverBinaries {
    if (!System.getenv().containsKey('GITHUB_ACTIONS')) {
        chromedriver {
            version = '2.45.0'
            fallbackTo32Bit = true
        }
        geckodriver '0.30.0'
    }
}

tasks.withType(Test) {
    systemProperty "geb.env", System.getProperty('geb.env')
    systemProperty "geb.build.reportsDir", reporting.file("geb/integrationTest")
    if (!System.getenv().containsKey('GITHUB_ACTIONS')) {
        systemProperty 'webdriver.chrome.driver', System.getProperty('webdriver.chrome.driver')
        systemProperty 'webdriver.gecko.driver', System.getProperty('webdriver.gecko.driver')
    } else {
        systemProperty 'webdriver.chrome.driver', "${System.getenv('CHROMEWEBDRIVER')}/chromedriver"
        systemProperty 'webdriver.gecko.driver', "${System.getenv('GECKOWEBDRIVER')}/geckodriver"
    }
}


assets {
    minifyJs = true
    minifyCss = true
}

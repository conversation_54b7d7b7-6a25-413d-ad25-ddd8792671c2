<%@ page import="grails.plugin.springsecurity.SpringSecurityUtils" %>
<!doctype html>
<html>
    <head>
        <title><g:if env="development">Grails Runtime Exception</g:if><g:else>Error</g:else></title>
        <g:if env="development"><asset:stylesheet src="errors.css"/></g:if>
        <style>
            .error-message {
                text-align: center;
                padding: 50px;
                font-family: Arial, sans-serif;
            }
            .error-message h2 {
                color: #d9534f;
                margin-bottom: 20px;
            }
            .error-message p {
                color: #666;
                font-size: 16px;
            }
        </style>
    </head>
    <body>
    <div id="content" role="main">
        <div class="container">
            <section class="row">
                <div class="col-12">
                <sec:ifAnyGranted roles="ROLE_GPT_MANAGER">
                    <g:if env="development">
                        <g:if test="${Throwable.isInstance(exception)}">
                            <g:renderException exception="${exception}" />
                        </g:if>
                        <g:elseif test="${request.getAttribute('javax.servlet.error.exception')}">
                            <g:renderException exception="${request.getAttribute('javax.servlet.error.exception')}" />
                        </g:elseif>
                        <g:else>
                            <ul class="errors">
                                <li>An error has occurred</li>
                                <li>Exception: ${exception}</li>
                                <li>Message: ${message}</li>
                                <li>Path: ${path}</li>
                            </ul>
                        </g:else>
                    </g:if>
                    <g:else>
                        <ul class="errors">
                            <li>An error has occurred</li>
                            <li>Exception: ${exception}</li>
                            <li>Message: ${message}</li>
                            <li>Path: ${path}</li>
                        </ul>
                    </g:else>
                </sec:ifAnyGranted>
                <sec:ifNotGranted roles="ROLE_DEBUG_DEVELOPER">
                    <div class="error-message">
                        <h2>Oops! Something went wrong</h2>
                        <p>We're sorry, but something went wrong. Please try again later or contact support if the problem persists.</p>
                    </div>
                </sec:ifNotGranted>
                </div>
            </section>
        </div>
    </div>
    </body>
</html>

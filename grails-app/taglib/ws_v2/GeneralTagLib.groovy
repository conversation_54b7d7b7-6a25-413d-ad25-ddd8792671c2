package ws_v2

class GeneralTagLib {
    //static defaultEncodeAs = [taglib:'html']
    //static encodeAsForTags = [tagName: [taglib:'html'], otherTagName: [taglib:'none']]

    def remoteFunction = { attrs, body ->

        out <<   "jQuery.ajax({method:'POST',data:${attrs.params}, url:'/${attrs.controller}/${attrs.action}',success:function(data,textStatus){${attrs.onSuccess};},error:function(XMLHttpRequest,textStatus,errorThrown){}});"

    }
}

